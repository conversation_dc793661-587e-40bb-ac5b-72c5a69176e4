# 开发指南

## 🚀 快速开始

### 1. 环境准备
- 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 注册微信小程序开发账号（可使用测试号）

### 2. 项目配置
1. 使用微信开发者工具打开项目根目录
2. 修改 `project.config.json` 中的 `appid` 字段为您的小程序 AppID
3. 如果没有 AppID，可以使用测试号或点击"不使用云服务"

### 3. 图标资源
由于版权原因，项目中不包含 TabBar 图标文件。请参考 `images/README.md` 添加所需图标：
- `images/home.png` 和 `images/home-active.png`
- `images/location.png` 和 `images/location-active.png`  
- `images/heart.png` 和 `images/heart-active.png`

或者临时移除 `app.json` 中 tabBar 的 iconPath 配置。

### 4. 运行项目
1. 在微信开发者工具中点击"编译"
2. 在模拟器中查看效果
3. 可以使用真机预览功能在手机上测试

## 📁 项目结构

```
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── project.config.json   # 项目配置文件
├── sitemap.json         # 站点地图配置
├── pages/               # 页面目录
│   ├── index/          # 首页
│   │   ├── index.js
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   └── index.json
│   ├── nearby/         # 附近景点页面
│   ├── favorites/      # 收藏页面
│   └── detail/         # 景点详情页面
├── utils/              # 工具函数
│   ├── util.js        # 通用工具函数
│   └── api.js         # API接口和AI助手
└── images/            # 图片资源
```

## 🔧 功能说明

### 1. 首页 (pages/index)
- 搜索景点功能
- 推荐景点展示
- 快速导航到附近景点和收藏页面
- 景点收藏/取消收藏

### 2. 附近景点 (pages/nearby)
- 获取用户位置权限
- 基于位置计算景点距离
- 支持按距离、评分、价格排序
- 一键导航到景点

### 3. 收藏管理 (pages/favorites)
- 查看收藏的景点列表
- 编辑模式：批量选择和删除
- 分享收藏列表
- 导出功能（模拟）

### 4. 景点详情 (pages/detail)
- 景点完整信息展示
- AI智能助手对话
- 常见问题快速回答
- 收藏、导航、电话联系功能

## 🤖 AI助手功能

AI助手使用模拟数据，支持以下类型的问题：
- 开放时间查询
- 门票价格咨询
- 交通路线指导
- 历史文化介绍
- 游览建议推荐
- 设施服务说明

## 📱 测试建议

### 功能测试
1. **首页测试**：搜索功能、景点展示、收藏操作
2. **定位测试**：位置权限申请、距离计算、排序功能
3. **收藏测试**：添加/删除收藏、编辑模式、分享功能
4. **详情测试**：信息展示、AI对话、操作按钮

### 兼容性测试
- 不同尺寸的手机屏幕
- iOS 和 Android 系统
- 不同版本的微信客户端

### 性能测试
- 页面加载速度
- 图片加载优化
- 内存使用情况

## 🔄 扩展开发

### 接入真实数据
1. 替换 `utils/api.js` 中的模拟数据
2. 接入真实的景点数据 API
3. 集成地图服务（腾讯地图、百度地图）

### 接入真实AI服务
1. 集成 ChatGPT、文心一言等 AI 服务
2. 实现语音对话功能
3. 添加图片识别功能

### 用户系统
1. 微信登录授权
2. 用户个人中心
3. 游览历史记录
4. 个性化推荐

### 社交功能
1. 景点评论和评分
2. 游记分享
3. 好友推荐
4. 打卡签到

## 🐛 常见问题

### 1. 编译错误
- 检查所有文件的语法是否正确
- 确保图片路径存在或移除相关配置

### 2. 定位失败
- 在真机上测试定位功能
- 检查小程序位置权限设置

### 3. 样式显示异常
- 检查 rpx 单位使用是否正确
- 验证 CSS 语法

### 4. 数据不显示
- 检查控制台错误信息
- 验证数据结构是否正确

## 📞 技术支持

如有问题，请：
1. 查看微信小程序官方文档
2. 检查开发者工具控制台错误
3. 参考项目 README.md 文件
