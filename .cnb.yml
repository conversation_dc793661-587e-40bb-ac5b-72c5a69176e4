name: CI

on:
  push:
    branches:
    - main

jobs:
  scf-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 
        uses: actions/checkout@main
      - name: Build 
        uses: docker://tencentcom/miniprogram-ci:latest
        env: 
          PLUGIN_APPID: wx0b31bbd370003bc2
          PLUGIN_PROJECTPATH: ./
          PLUGIN_PRIVATEKEYPATH: https://cnb.cool/iptton/keys/-/blob/6539797756e05423a3f41063f13a1648d84904a0/habitKey.key
          PLUGIN_VERSION: v1.0.0