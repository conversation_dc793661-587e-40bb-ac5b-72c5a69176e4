# 景点助手微信小程序

一个功能完整的景点助手微信小程序，集成了AI智能咨询功能，帮助用户发现和了解周边景点。

## 🌟 主要功能

### 1. 首页推荐
- **热门景点展示**：精选推荐景点，包含图片、评分、距离等信息
- **快速导航**：一键跳转到附近景点和收藏页面
- **搜索功能**：支持景点名称搜索（预留接口）
- **收藏功能**：一键收藏/取消收藏景点

### 2. 附近景点
- **智能定位**：自动获取用户位置，计算景点距离
- **多种排序**：支持按距离、评分、价格排序
- **权限管理**：友好的位置权限申请和错误处理
- **导航功能**：一键导航到目标景点

### 3. 收藏管理
- **收藏列表**：查看所有收藏的景点
- **编辑模式**：批量管理收藏项目
- **分享功能**：分享收藏列表给朋友
- **导出功能**：导出收藏列表（模拟功能）

### 4. AI智能助手 🤖
- **景点详情**：展示景点完整信息和高清图片
- **智能对话**：基于LLM的景点咨询助手
- **常见问题**：快速回答开放时间、门票价格、交通路线等
- **历史文化**：深度介绍景点的历史背景和文化内涵
- **游览建议**：个性化的游览路线和时间安排建议

## 🛠️ 技术特点

### 前端技术
- **原生微信小程序**：使用微信小程序原生框架开发
- **响应式设计**：适配不同尺寸的移动设备
- **现代UI设计**：采用渐变色彩和卡片式布局
- **流畅动画**：丰富的交互动画和过渡效果

### 核心功能
- **模拟AI对话**：智能的景点问答系统
- **地理位置服务**：精确的距离计算和导航
- **本地数据存储**：收藏数据持久化保存
- **状态管理**：全局数据状态管理

### 用户体验
- **加载状态**：友好的加载动画和提示
- **错误处理**：完善的错误提示和重试机制
- **无障碍设计**：支持无障碍访问
- **分享功能**：支持分享到微信好友和朋友圈

## 📱 页面结构

```
├── 首页 (index)
│   ├── 搜索栏
│   ├── 快速导航
│   └── 推荐景点列表
├── 附近景点 (nearby)
│   ├── 位置状态栏
│   ├── 排序选择器
│   └── 景点列表
├── 收藏 (favorites)
│   ├── 操作栏
│   ├── 收藏列表
│   └── 编辑功能
└── 景点详情 (detail)
    ├── 景点信息标签页
    │   ├── 基本信息
    │   ├── 详细介绍
    │   └── 操作按钮
    └── AI助手标签页
        ├── 聊天消息列表
        ├── 快速问题
        └── 输入框
```

## 🚀 快速开始

### 环境要求
- 微信开发者工具
- 微信小程序开发账号

### 安装步骤
1. 下载或克隆项目代码
2. 使用微信开发者工具打开项目
3. 配置小程序AppID（可使用测试号）
4. 点击编译运行

### 配置说明
- 修改 `project.config.json` 中的 `appid` 字段
- 如需真实地理位置功能，需要在微信公众平台配置相关权限
- 项目已完成开发，可直接运行

### 项目状态
✅ **项目已完成** - 所有功能已实现，可直接在微信开发者工具中运行

## 🎯 使用指南

### 基本操作
1. **浏览景点**：在首页查看推荐景点，点击卡片查看详情
2. **查找附近**：切换到"附近"标签，获取基于位置的景点推荐
3. **收藏管理**：点击心形图标收藏景点，在"收藏"标签管理收藏列表
4. **AI咨询**：在景点详情页切换到"AI助手"标签，与智能助手对话

### AI助手使用技巧
- 询问开放时间：「这里几点开门？」
- 了解门票价格：「门票多少钱？」
- 获取交通信息：「怎么去这里？」
- 学习历史文化：「有什么历史故事？」
- 获取游览建议：「有什么推荐的游览路线？」

## 🔧 开发说明

### 项目结构
```
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── pages/                # 页面目录
│   ├── index/           # 首页
│   ├── nearby/          # 附近景点
│   ├── favorites/       # 收藏页面
│   └── detail/          # 景点详情
├── utils/               # 工具函数
│   ├── util.js         # 通用工具函数
│   └── api.js          # API接口和AI助手
└── images/             # 图片资源
```

### 数据模型
景点数据结构：
```javascript
{
  id: 1,
  name: "景点名称",
  image: "图片URL",
  rating: 4.8,
  distance: "2.3km",
  price: "60元",
  openTime: "08:30-17:00",
  description: "景点描述",
  location: {
    latitude: 39.9163,
    longitude: 116.3972
  },
  tags: ["标签1", "标签2"],
  isRecommended: true
}
```

### 扩展开发
- **真实API集成**：替换模拟数据为真实的景点API
- **真实AI集成**：接入ChatGPT、文心一言等真实AI服务
- **用户系统**：添加用户登录和个人中心功能
- **社交功能**：添加评论、评分、分享等社交功能
- **地图集成**：集成腾讯地图或百度地图SDK

## 📄 许可证

本项目仅供学习和演示使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者邮箱

---

**注意**：这是一个演示项目，AI助手功能使用模拟数据。在生产环境中，建议集成真实的AI服务和景点数据API。
