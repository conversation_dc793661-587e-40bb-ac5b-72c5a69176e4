<view class="container">
  <view class="location-status">
    <view wx:if="{{locationStatus === 'loading'}}" class="status-item loading">
      <view class="status-icon">��</view>
      <view class="status-text">正在获取位置...</view>
    </view>
    
    <view wx:elif="{{locationStatus === 'success'}}" class="status-item success">
      <view class="status-icon">✅</view>
      <view class="status-text">定位成功，为您推荐附近景点</view>
    </view>
    
    <view wx:elif="{{locationStatus === 'denied'}}" class="status-item error">
      <view class="status-icon">❌</view>
      <view class="status-text">位置权限被拒绝</view>
      <view class="retry-btn" bindtap="onRetryLocation">重新授权</view>
    </view>
    
    <view wx:else class="status-item error">
      <view class="status-icon">⚠️</view>
      <view class="status-text">定位失败，显示默认景点</view>
      <view class="retry-btn" bindtap="onRetryLocation">重试</view>
    </view>
  </view>

  <view class="sort-section">
    <view class="sort-label">排序方式：</view>
    <picker 
      class="sort-picker" 
      mode="selector" 
      range="{{sortOptions}}" 
      range-key="label"
      value="{{sortBy}}"
      bindchange="onSortChange"
    >
      <view class="picker-text">
        {{sortOptions[sortBy === 'distance' ? 0 : sortBy === 'rating' ? 1 : 2].label}} ▼
      </view>
    </picker>
  </view>

  <view wx:if="{{loading}}" class="loading">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <view wx:else class="attractions-list">
    <view 
      wx:for="{{attractions}}" 
      wx:key="id" 
      class="attraction-card"
      data-id="{{item.id}}"
      bindtap="onAttractionTap"
    >
      <view class="card-image-wrapper">
        <image class="card-image" src="{{item.image}}" mode="aspectFill" lazy-load />
        <view class="favorite-btn" data-attraction="{{item}}" bindtap="onFavoriteTap">
          <view class="favorite-icon {{item.isFavorite ? 'active' : ''}}">
            {{item.isFavorite ? '❤️' : '🤍'}}
          </view>
        </view>
        <view class="distance-badge">{{item.distance}}</view>
      </view>

      <view class="card-content">
        <view class="card-header">
          <view class="attraction-name">{{item.name}}</view>
          <view class="attraction-rating">
            <view class="rating-star">⭐</view>
            <view class="rating-value">{{item.rating}}</view>
          </view>
        </view>

        <view class="attraction-info">
          <view class="info-item">
            <view class="info-icon">💰</view>
            <view class="info-text">{{item.price}}</view>
          </view>
          <view class="info-item">
            <view class="info-icon">🕐</view>
            <view class="info-text">{{item.openTime}}</view>
          </view>
        </view>

        <view class="attraction-desc">{{item.description}}</view>

        <view class="card-actions">
          <view class="attraction-tags">
            <view 
              wx:for="{{item.tags}}" 
              wx:for-item="tag" 
              wx:key="*this" 
              class="tag"
            >
              {{tag}}
            </view>
          </view>
          <view class="navigate-btn" data-attraction="{{item}}" bindtap="onNavigateTap">
            <view class="navigate-icon">🧭</view>
            <view class="navigate-text">导航</view>
          </view>
        </view>
      </view>
    </view>

    <view wx:if="{{attractions.length === 0 && !loading}}" class="empty-state">
      <view class="empty-state-icon">🗺️</view>
      <view class="empty-state-text">附近暂无景点信息</view>
    </view>
  </view>
</view>
