// pages/nearby/nearby.js
const app = getApp()
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    attractions: [],
    loading: true,
    locationStatus: 'loading', // loading, success, failed, denied
    userLocation: null,
    sortBy: 'distance', // distance, rating, price
    sortOptions: [
      { value: 'distance', label: '距离优先' },
      { value: 'rating', label: '评分优先' },
      { value: 'price', label: '价格优先' }
    ]
  },

  onLoad() {
    this.getUserLocationAndLoadAttractions()
  },

  onShow() {
    // 更新收藏状态
    this.updateFavoriteStatus()
  },

  onPullDownRefresh() {
    this.getUserLocationAndLoadAttractions().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 获取用户位置并加载景点
  getUserLocationAndLoadAttractions() {
    this.setData({ 
      loading: true, 
      locationStatus: 'loading' 
    })

    return app.getUserLocation().then(location => {
      this.setData({
        userLocation: location,
        locationStatus: 'success'
      })
      return this.loadNearbyAttractions()
    }).catch(err => {
      console.error('获取位置失败', err)
      
      if (err.errMsg && err.errMsg.includes('auth deny')) {
        this.setData({ locationStatus: 'denied' })
        this.showLocationPermissionDialog()
      } else {
        this.setData({ locationStatus: 'failed' })
      }
      
      // 即使定位失败也加载景点数据（使用模拟距离）
      return this.loadNearbyAttractions()
    }).finally(() => {
      this.setData({ loading: false })
    })
  },

  // 加载附近景点
  loadNearbyAttractions() {
    return api.getAttractions({ sortBy: this.data.sortBy }).then(res => {
      if (res.success) {
        let attractions = res.data.map(item => ({
          ...item,
          isFavorite: app.isFavorite(item.id)
        }))

        // 如果有用户位置，重新计算距离
        if (this.data.userLocation) {
          attractions = this.calculateDistances(attractions)
        }

        this.setData({ attractions })
      } else {
        util.showError('加载失败')
      }
    }).catch(err => {
      console.error('加载附近景点失败', err)
      util.showError('网络错误')
    })
  },

  // 计算景点距离
  calculateDistances(attractions) {
    const { latitude, longitude } = this.data.userLocation
    
    return attractions.map(item => {
      const distance = app.calculateDistance(
        latitude, longitude,
        item.location.latitude, item.location.longitude
      )
      return {
        ...item,
        distance: app.formatDistance(distance),
        distanceValue: distance
      }
    }).sort((a, b) => {
      if (this.data.sortBy === 'distance') {
        return a.distanceValue - b.distanceValue
      }
      return 0
    })
  },

  // 更新收藏状态
  updateFavoriteStatus() {
    const attractions = this.data.attractions.map(item => ({
      ...item,
      isFavorite: app.isFavorite(item.id)
    }))
    this.setData({ attractions })
  },

  // 显示位置权限对话框
  showLocationPermissionDialog() {
    wx.showModal({
      title: '位置权限',
      content: '需要获取您的位置信息来推荐附近景点，请在设置中开启位置权限',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.userLocation']) {
                this.getUserLocationAndLoadAttractions()
              }
            }
          })
        }
      }
    })
  },

  // 重新获取位置
  onRetryLocation() {
    this.getUserLocationAndLoadAttractions()
  },

  // 排序方式改变
  onSortChange(e) {
    const sortBy = e.detail.value
    this.setData({ sortBy })
    
    util.showLoading('排序中...')
    this.loadNearbyAttractions().finally(() => {
      util.hideLoading()
    })
  },

  // 景点卡片点击
  onAttractionTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 收藏按钮点击
  onFavoriteTap(e) {
    // 安全检查事件对象
    if (e && e.stopPropagation) {
      e.stopPropagation()
    }

    // 检查事件对象和数据
    if (!e || !e.currentTarget || !e.currentTarget.dataset) {
      util.showError('操作失败，请重试')
      return
    }

    const { attraction } = e.currentTarget.dataset
    if (!attraction) {
      util.showError('景点信息不存在')
      return
    }

    const { id, isFavorite } = attraction

    if (isFavorite) {
      if (app.removeFavorite(id)) {
        util.showSuccess('已取消收藏')
        this.updateFavoriteStatus()
      }
    } else {
      if (app.addFavorite(attraction)) {
        util.showSuccess('已添加收藏')
        this.updateFavoriteStatus()
      } else {
        util.showError('已经收藏过了')
      }
    }
  },

  // 导航到景点
  onNavigateTap(e) {
    // 安全检查事件对象
    if (e && e.stopPropagation) {
      e.stopPropagation()
    }

    // 检查事件对象和数据
    if (!e || !e.currentTarget || !e.currentTarget.dataset) {
      util.showError('操作失败，请重试')
      return
    }

    const { attraction } = e.currentTarget.dataset
    if (!attraction || !attraction.location) {
      util.showError('景点位置信息不存在')
      return
    }

    const { latitude, longitude, name } = attraction.location
    
    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: attraction.name,
      address: attraction.detailInfo ? attraction.detailInfo.address : '',
      scale: 18,
      success: () => {
        console.log('打开地图成功')
      },
      fail: (err) => {
        console.log('打开地图失败', err)
        util.showError('打开地图失败')
      }
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '发现附近的精彩景点',
      path: '/pages/nearby/nearby'
    }
  }
})
