/* pages/nearby/nearby.wxss */
.container {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 位置状态栏 */
.location-status {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.status-item.loading .status-icon {
  animation: pulse 1.5s ease-in-out infinite;
}

.status-item.success {
  color: #28a745;
}

.status-item.error {
  color: #dc3545;
}

.status-icon {
  font-size: 32rpx;
}

.status-text {
  flex: 1;
  font-size: 28rpx;
}

.retry-btn {
  background: #4A90E2;
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 排序选择器 */
.sort-section {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.sort-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.sort-picker {
  flex: 1;
}

.picker-text {
  background: #f8f9fa;
  padding: 16rpx 24rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #4A90E2;
  text-align: center;
  border: 2rpx solid #e9ecef;
}

/* 景点列表 */
.attractions-list {
  padding: 0 20rpx 40rpx;
}

.attraction-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.attraction-card:active {
  transform: scale(0.98);
}

/* 图片区域 */
.card-image-wrapper {
  position: relative;
  height: 350rpx;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.favorite-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.favorite-icon {
  font-size: 32rpx;
  transition: transform 0.2s ease;
}

.favorite-icon.active {
  transform: scale(1.2);
}

.distance-badge {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

/* 内容区域 */
.card-content {
  padding: 30rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.attraction-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.attraction-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-star {
  font-size: 24rpx;
}

.rating-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff9500;
}

/* 信息行 */
.attraction-info {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-icon {
  font-size: 24rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
}

/* 描述文本 */
.attraction-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 卡片操作区域 */
.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.attraction-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  flex: 1;
}

.tag {
  background: #f0f8ff;
  color: #4A90E2;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: 1rpx solid #e6f3ff;
}

.navigate-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: #fff;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.navigate-icon {
  font-size: 24rpx;
}

.navigate-text {
  font-weight: 500;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}
