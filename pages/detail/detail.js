// pages/detail/detail.js
const app = getApp()
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    attractionId: null,
    attraction: null,
    loading: true,
    isFavorite: false,
    activeTab: 'info', // info, ai

    // AI助手相关
    messages: [],
    inputValue: '',
    quickQuestions: [
      '这里几点开门？',
      '门票多少钱？',
      '怎么去这里？',
      '有什么历史故事？',
      '推荐游览路线'
    ],
    aiLoading: false,

    // 滚动位置记录
    infoTabScrollTop: 0,
    isTabSwitching: false,
    tabNavHeight: 0,
    heroImageHeight: 0
  },

  onLoad(options) {
    const { id } = options
    if (id) {
      this.setData({ attractionId: parseInt(id) })
      this.loadAttractionDetail()
    } else {
      util.showError('景点ID不存在')
      wx.navigateBack()
    }
  },

  onShow() {
    // 更新收藏状态
    if (this.data.attractionId) {
      this.setData({
        isFavorite: app.isFavorite(this.data.attractionId)
      })
    }
  },

  // 加载景点详情
  loadAttractionDetail() {
    this.setData({ loading: true })
    
    api.getAttractionDetail(this.data.attractionId).then(res => {
      if (res.success) {
        this.setData({
          attraction: res.data,
          isFavorite: app.isFavorite(res.data.id),
          loading: false
        })
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: res.data.name
        })
        
        // 初始化AI对话
        this.initAIChat()

        // 获取关键元素高度
        this.getElementHeights()
      } else {
        util.showError('景点不存在')
        this.setData({ loading: false })
        wx.navigateBack()
      }
    }).catch(err => {
      console.error('加载景点详情失败', err)
      util.showError('网络错误')
      this.setData({ loading: false })
    })
  },

  // 初始化AI对话
  initAIChat() {
    const welcomeMessage = {
      id: util.generateUUID(),
      type: 'ai',
      content: `您好！我是${this.data.attraction.name}的专属导览助手。我可以为您介绍这里的历史文化、开放时间、门票价格、交通路线等信息。有什么想了解的吗？`,
      timestamp: new Date().getTime()
    }

    this.setData({
      messages: [welcomeMessage]
    })
  },

  // 获取关键元素高度
  getElementHeights() {
    setTimeout(() => {
      const query = wx.createSelectorQuery()

      // 获取Tab导航高度
      query.select('.tab-nav').boundingClientRect((rect) => {
        if (rect) {
          this.setData({ tabNavHeight: rect.height })
        }
      })

      // 获取英雄图片高度
      query.select('.hero-image').boundingClientRect((rect) => {
        if (rect) {
          this.setData({ heroImageHeight: rect.height })
        }
      })

      query.exec()
    }, 500) // 等待页面渲染完成
  },

  // 切换标签页
  onTabChange(e) {
    const { tab } = e.currentTarget.dataset
    const currentTab = this.data.activeTab

    // 如果点击的是当前标签页，不做任何操作
    if (tab === currentTab) {
      return
    }

    // 设置切换状态，防止滚动事件干扰
    this.setData({ isTabSwitching: true })

    // 获取当前滚动位置
    wx.createSelectorQuery().selectViewport().scrollOffset((res) => {
      const currentScrollTop = res.scrollTop
      const { heroImageHeight } = this.data

      // 计算Tab导航的固定位置（英雄图片高度）
      const tabNavFixedPosition = heroImageHeight

      // 如果当前在景点信息页，记录滚动位置
      if (currentTab === 'info') {
        this.setData({
          infoTabScrollTop: currentScrollTop,
          activeTab: tab
        })

        // 如果切换到AI助手
        if (tab === 'ai') {
          // 如果Tab导航已经固定（滚动位置超过英雄图片高度），保持在固定位置
          const targetScrollTop = currentScrollTop >= tabNavFixedPosition ? tabNavFixedPosition : 0

          setTimeout(() => {
            wx.pageScrollTo({
              scrollTop: targetScrollTop,
              duration: 300,
              success: () => {
                this.setData({ isTabSwitching: false })
              }
            })
          }, 50)
        } else {
          this.setData({ isTabSwitching: false })
        }
      }
      // 如果当前在AI助手页，切换到景点信息页
      else if (currentTab === 'ai' && tab === 'info') {
        this.setData({ activeTab: tab })

        // 恢复景点信息页的滚动位置，但确保Tab导航位置不跳变
        let targetScrollTop = this.data.infoTabScrollTop

        // 如果当前Tab导航是固定状态，确保恢复后也保持固定
        if (currentScrollTop >= tabNavFixedPosition && targetScrollTop < tabNavFixedPosition) {
          targetScrollTop = tabNavFixedPosition
        }

        setTimeout(() => {
          wx.pageScrollTo({
            scrollTop: targetScrollTop,
            duration: 300,
            success: () => {
              this.setData({ isTabSwitching: false })
            }
          })
        }, 50)
      } else {
        // 其他情况直接切换
        this.setData({
          activeTab: tab,
          isTabSwitching: false
        })
      }
    }).exec()
  },

  // 收藏/取消收藏
  onToggleFavorite() {
    const { attraction, isFavorite } = this.data
    
    if (isFavorite) {
      if (app.removeFavorite(attraction.id)) {
        util.showSuccess('已取消收藏')
        this.setData({ isFavorite: false })
      }
    } else {
      if (app.addFavorite(attraction)) {
        util.showSuccess('已添加收藏')
        this.setData({ isFavorite: true })
      } else {
        util.showError('已经收藏过了')
      }
    }
  },

  // 导航到景点
  onNavigate() {
    const { attraction } = this.data
    
    if (!attraction.location) {
      util.showError('暂无位置信息')
      return
    }
    
    const { latitude, longitude } = attraction.location
    
    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: attraction.name,
      address: attraction.detailInfo ? attraction.detailInfo.address : '',
      scale: 18,
      success: () => {
        console.log('打开地图成功')
      },
      fail: (err) => {
        console.log('打开地图失败', err)
        util.showError('打开地图失败')
      }
    })
  },

  // 拨打电话
  onCall() {
    const { attraction } = this.data

    if (!attraction.detailInfo || !attraction.detailInfo.phone) {
      util.showError('暂无联系电话')
      return
    }

    wx.makePhoneCall({
      phoneNumber: attraction.detailInfo.phone,
      success: () => {
        console.log('拨打电话成功')
      },
      fail: (err) => {
        console.log('拨打电话失败或取消', err)
        // 用户取消拨打电话是正常行为，不需要显示错误提示
        if (err.errMsg && !err.errMsg.includes('cancel')) {
          util.showError('拨打电话失败')
        }
      }
    })
  },

  // AI输入框内容变化
  onAIInput(e) {
    this.setData({ inputValue: e.detail.value })
  },

  // 发送AI消息
  onSendMessage() {
    const { inputValue } = this.data
    const question = inputValue.trim()
    
    if (!question) {
      util.showError('请输入问题')
      return
    }
    
    this.sendAIMessage(question)
    this.setData({ inputValue: '' })
  },

  // 快速问题点击
  onQuickQuestion(e) {
    const { question } = e.currentTarget.dataset
    this.sendAIMessage(question)
  },

  // 发送AI消息
  sendAIMessage(question) {
    const userMessage = {
      id: util.generateUUID(),
      type: 'user',
      content: question,
      timestamp: new Date().getTime()
    }
    
    // 添加用户消息
    const messages = [...this.data.messages, userMessage]
    this.setData({ 
      messages,
      aiLoading: true 
    })
    
    // 滚动到底部
    this.scrollToBottom()
    
    // 获取AI回复
    api.getAIResponse(question, this.data.attraction).then(res => {
      if (res.success) {
        const aiMessage = {
          id: util.generateUUID(),
          type: 'ai',
          content: res.data.message,
          timestamp: res.data.timestamp
        }
        
        this.setData({
          messages: [...this.data.messages, aiMessage],
          aiLoading: false
        })
        
        this.scrollToBottom()
      } else {
        util.showError('AI助手暂时无法回复')
        this.setData({ aiLoading: false })
      }
    }).catch(err => {
      console.error('AI回复失败', err)
      util.showError('网络错误')
      this.setData({ aiLoading: false })
    })
  },

  // 滚动到底部
  scrollToBottom() {
    // 只有在AI助手页面且不在切换状态时才滚动
    if (this.data.activeTab === 'ai' && !this.data.isTabSwitching) {
      setTimeout(() => {
        wx.pageScrollTo({
          scrollTop: 999999,
          duration: 300
        })
      }, 100)
    }
  },

  // 分享功能
  onShareAppMessage() {
    const { attraction } = this.data
    return {
      title: `${attraction.name} - ${attraction.description}`,
      path: `/pages/detail/detail?id=${attraction.id}`,
      imageUrl: attraction.image
    }
  }
})
