/* pages/detail/detail.wxss */
.container {
  background: #f8f9fa;
  min-height: 100vh;
}

.detail-content {
  position: relative;
}

/* 英雄图片区域 */
.hero-image {
  position: relative;
  height: 500rpx;
  overflow: hidden;
}

.hero-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 60rpx 30rpx 30rpx;
  color: #fff;
}

.hero-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 15rpx;
}

.hero-rating {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.rating-star {
  font-size: 28rpx;
}

.rating-value {
  font-size: 32rpx;
  font-weight: 600;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: #fff;
  border-bottom: 2rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 999;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 标签页内容容器 */
.tab-content-wrapper {
  min-height: calc(100vh - 100rpx);
  position: relative;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #4A90E2;
  border-bottom: 4rpx solid #4A90E2;
}

.tab-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tab-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 信息标签页 */
.info-tab {
  padding: 30rpx;
  min-height: calc(100vh - 200rpx);
}

.info-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 25rpx;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 24rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 描述和历史 */
.description, .history-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.history {
  margin-top: 25rpx;
  padding-top: 25rpx;
  border-top: 2rpx solid #f0f0f0;
}

.history-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
}

/* 标签列表 */
.tags-list {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.tag {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 设施列表 */
.facilities-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.facility-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.facility-icon {
  font-size: 24rpx;
}

.facility-text {
  font-size: 26rpx;
  color: #333;
}

/* 提示列表 */
.tips-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.tip-icon {
  font-size: 24rpx;
  margin-top: 4rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 25rpx 20rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.favorite {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.action-btn.favorite.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  border-color: transparent;
}

.action-btn.navigate {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: #fff;
}

.action-btn.call {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 24rpx;
}

/* AI标签页 */
.ai-tab {
  padding: 30rpx;
  padding-bottom: 200rpx;
  min-height: calc(100vh - 200rpx);
}

/* 消息容器 */
.messages-container {
  margin-bottom: 40rpx;
}

.message-item {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  flex-shrink: 0;
}

.message-item.user .message-avatar {
  background: #4A90E2;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-text {
  background: #fff;
  padding: 20rpx 25rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.message-item.user .message-text {
  background: #4A90E2;
  color: #fff;
}

.message-time {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  text-align: center;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  gap: 8rpx;
  padding: 20rpx 25rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ccc;
  animation: typing 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10rpx);
    opacity: 1;
  }
}

/* 快速问题 */
.quick-questions {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.quick-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 25rpx;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.question-item {
  background: #f8f9fa;
  padding: 20rpx 25rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #4A90E2;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.question-item:active {
  background: #4A90E2;
  color: #fff;
  transform: scale(0.98);
}

/* 输入区域 */
.input-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  z-index: 100;
}

.input-wrapper {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.message-input {
  flex: 1;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 25rpx;
  padding: 20rpx 25rpx;
  font-size: 28rpx;
}

.send-btn {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 28rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}
