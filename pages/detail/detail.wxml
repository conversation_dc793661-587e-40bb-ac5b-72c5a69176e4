<!--pages/detail/detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 景点详情 -->
  <view wx:else class="detail-content">
    <!-- 景点图片 -->
    <view class="hero-image">
      <image class="hero-img" src="{{attraction.image}}" mode="aspectFill" />
      <view class="hero-overlay">
        <view class="hero-title">{{attraction.name}}</view>
        <view class="hero-rating">
          <view class="rating-star">⭐</view>
          <view class="rating-value">{{attraction.rating}}</view>
        </view>
      </view>
    </view>

    <!-- 标签页导航 -->
    <view class="tab-nav">
      <view 
        class="tab-item {{activeTab === 'info' ? 'active' : ''}}"
        data-tab="info"
        bindtap="onTabChange"
      >
        <view class="tab-icon">ℹ️</view>
        <view class="tab-text">景点信息</view>
      </view>
      <view 
        class="tab-item {{activeTab === 'ai' ? 'active' : ''}}"
        data-tab="ai"
        bindtap="onTabChange"
      >
        <view class="tab-icon">🤖</view>
        <view class="tab-text">AI助手</view>
      </view>
    </view>

    <!-- 标签页内容容器 -->
    <view class="tab-content-wrapper">
      <!-- 景点信息标签页 -->
      <view wx:if="{{activeTab === 'info'}}" class="info-tab">
        <!-- 基本信息 -->
        <view class="info-section">
          <view class="section-title">📍 基本信息</view>
          <view class="info-grid">
            <view class="info-item">
              <view class="info-label">距离</view>
              <view class="info-value">{{attraction.distance}}</view>
            </view>
            <view class="info-item">
              <view class="info-label">门票</view>
              <view class="info-value">{{attraction.price}}</view>
            </view>
            <view class="info-item">
              <view class="info-label">开放时间</view>
              <view class="info-value">{{attraction.openTime}}</view>
            </view>
            <view wx:if="{{attraction.detailInfo.address}}" class="info-item full-width">
              <view class="info-label">地址</view>
              <view class="info-value">{{attraction.detailInfo.address}}</view>
            </view>
          </view>
        </view>

        <!-- 详细介绍 -->
        <view class="info-section">
          <view class="section-title">📖 详细介绍</view>
          <view class="description">{{attraction.description}}</view>
          <view wx:if="{{attraction.detailInfo.history}}" class="history">
            <view class="history-title">历史背景</view>
            <view class="history-content">{{attraction.detailInfo.history}}</view>
          </view>
        </view>

        <!-- 标签 -->
        <view class="info-section">
          <view class="section-title">🏷️ 景点标签</view>
          <view class="tags-list">
            <view 
              wx:for="{{attraction.tags}}" 
              wx:key="*this" 
              class="tag"
            >
              {{item}}
            </view>
          </view>
        </view>

        <!-- 设施服务 -->
        <view wx:if="{{attraction.detailInfo.facilities}}" class="info-section">
          <view class="section-title">🏢 设施服务</view>
          <view class="facilities-list">
            <view 
              wx:for="{{attraction.detailInfo.facilities}}" 
              wx:key="*this" 
              class="facility-item"
            >
              <view class="facility-icon">✅</view>
              <view class="facility-text">{{item}}</view>
            </view>
          </view>
        </view>

        <!-- 游览提示 -->
        <view wx:if="{{attraction.detailInfo.tips}}" class="info-section">
          <view class="section-title">💡 游览提示</view>
          <view class="tips-list">
            <view 
              wx:for="{{attraction.detailInfo.tips}}" 
              wx:key="*this" 
              class="tip-item"
            >
              <view class="tip-icon">💡</view>
              <view class="tip-text">{{item}}</view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <view class="action-btn favorite {{isFavorite ? 'active' : ''}}" bindtap="onToggleFavorite">
            <view class="btn-icon">{{isFavorite ? '❤️' : '🤍'}}</view>
            <view class="btn-text">{{isFavorite ? '已收藏' : '收藏'}}</view>
          </view>
          <view class="action-btn navigate" bindtap="onNavigate">
            <view class="btn-icon">🧭</view>
            <view class="btn-text">导航</view>
          </view>
          <view wx:if="{{attraction.detailInfo.phone}}" class="action-btn call" bindtap="onCall">
            <view class="btn-icon">📞</view>
            <view class="btn-text">电话</view>
          </view>
        </view>
      </view> <!-- 添加这个结束标签 -->

      <!-- AI助手标签页 -->
      <view wx:if="{{activeTab === 'ai'}}" class="ai-tab">
        <!-- 聊天消息列表 -->
        <view class="messages-container">
          <view 
            wx:for="{{messages}}" 
            wx:key="id" 
            class="message-item {{item.type}}"
          >
            <view class="message-avatar">
              {{item.type === 'user' ? '👤' : '🤖'}}
            </view>
            <view class="message-content">
              <view class="message-text">{{item.content}}</view>
              <view class="message-time">{{item.timestamp}}</view>
            </view>
          </view>
          
          <!-- AI加载状态 -->
          <view wx:if="{{aiLoading}}" class="message-item ai loading">
            <view class="message-avatar">🤖</view>
            <view class="message-content">
              <view class="typing-indicator">
                <view class="typing-dot"></view>
                <view class="typing-dot"></view>
                <view class="typing-dot"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 快速问题 -->
        <view class="quick-questions">
          <view class="quick-title">💬 常见问题</view>
          <view class="questions-list">
            <view 
              wx:for="{{quickQuestions}}" 
              wx:key="*this" 
              class="question-item"
              data-question="{{item}}"
              bindtap="onQuickQuestion"
            >
              {{item}}
            </view>
          </view>
        </view>

        <!-- 输入框 -->
        <view class="input-section">
          <view class="input-wrapper">
            <input 
              class="message-input" 
              placeholder="请输入您的问题..." 
              value="{{inputValue}}"
              bindinput="onAIInput"
              bindconfirm="onSendMessage"
              confirm-type="send"
            />
            <view class="send-btn" bindtap="onSendMessage">
              <view class="send-icon">📤</view>
            </view>
          </view>
        </view>
      </view> <!-- 添加这个结束标签 -->
    </view>
  </view>
</view>
