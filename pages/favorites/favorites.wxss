/* pages/favorites/favorites.wxss */
.container {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.favorites-count {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #4A90E2;
  color: #fff;
  padding: 12rpx 20rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-weight: 500;
}

/* 编辑模式操作栏 */
.edit-actions {
  display: flex;
  gap: 20rpx;
  padding: 0 20rpx 20rpx;
}

.edit-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: #fff;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  font-size: 26rpx;
  color: #333;
}

/* 功能按钮 */
.function-buttons {
  display: flex;
  gap: 20rpx;
  padding: 0 20rpx 20rpx;
}

.function-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: #fff;
  padding: 20rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: 500;
}

/* 收藏列表 */
.favorites-list {
  padding: 0 20rpx 40rpx;
}

.favorite-card {
  display: flex;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.favorite-card:active {
  transform: scale(0.98);
}

.favorite-card.edit-mode {
  padding-left: 20rpx;
}

.favorite-card.selected {
  border: 4rpx solid #4A90E2;
}

/* 选择框 */
.select-checkbox {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx 30rpx 0;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #fff;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background: #4A90E2;
  border-color: #4A90E2;
}

/* 图片区域 */
.card-image-wrapper {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  flex-shrink: 0;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-btn {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.remove-icon {
  font-size: 20rpx;
}

/* 内容区域 */
.card-content {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.attraction-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.attraction-rating {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.rating-star {
  font-size: 20rpx;
}

.rating-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #ff9500;
}

/* 信息行 */
.attraction-info {
  display: flex;
  gap: 20rpx;
  margin-bottom: 15rpx;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.info-icon {
  font-size: 20rpx;
}

.info-text {
  font-size: 22rpx;
  color: #666;
}

/* 描述文本 */
.attraction-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  flex: 1;
}

/* 卡片操作区域 */
.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.attraction-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
  flex: 1;
}

.tag {
  background: #f0f8ff;
  color: #4A90E2;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
  border: 1rpx solid #e6f3ff;
}

.navigate-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: #fff;
  padding: 10rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin-left: 15rpx;
}

.navigate-icon {
  font-size: 20rpx;
}

.navigate-text {
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-state-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-state-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.empty-state-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.goto-home-btn {
  display: inline-block;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}
