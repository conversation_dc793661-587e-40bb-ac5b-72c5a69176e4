// pages/favorites/favorites.js
const app = getApp()
const util = require('../../utils/util.js')

Page({
  data: {
    favorites: [],
    editMode: false,
    selectedItems: [],
    loading: true
  },

  onLoad() {
    this.loadFavorites()
  },

  onShow() {
    this.loadFavorites()
  },

  onPullDownRefresh() {
    this.loadFavorites().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载收藏列表
  loadFavorites() {
    this.setData({ loading: true })
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const favorites = app.globalData.favorites || []
        this.setData({ 
          favorites,
          loading: false,
          selectedItems: []
        })
        resolve()
      }, 300)
    })
  },

  // 切换编辑模式
  toggleEditMode() {
    const editMode = !this.data.editMode
    this.setData({ 
      editMode,
      selectedItems: editMode ? [] : this.data.selectedItems
    })
  },

  // 选择/取消选择项目
  onItemSelect(e) {
    const { id } = e.currentTarget.dataset
    const { selectedItems } = this.data
    const index = selectedItems.indexOf(id)
    
    if (index > -1) {
      selectedItems.splice(index, 1)
    } else {
      selectedItems.push(id)
    }
    
    this.setData({ selectedItems })
  },

  // 全选/取消全选
  onSelectAll() {
    const { favorites, selectedItems } = this.data
    const allSelected = selectedItems.length === favorites.length
    
    this.setData({
      selectedItems: allSelected ? [] : favorites.map(item => item.id)
    })
  },

  // 删除选中项目
  onDeleteSelected() {
    const { selectedItems, favorites } = this.data
    
    if (selectedItems.length === 0) {
      util.showError('请选择要删除的项目')
      return
    }
    
    util.showConfirm(`确定要删除${selectedItems.length}个收藏项目吗？`).then(confirmed => {
      if (confirmed) {
        selectedItems.forEach(id => {
          app.removeFavorite(id)
        })
        
        util.showSuccess('删除成功')
        this.loadFavorites()
        this.setData({ editMode: false })
      }
    })
  },

  // 分享收藏列表
  onShareFavorites() {
    const { favorites } = this.data
    
    if (favorites.length === 0) {
      util.showError('收藏列表为空')
      return
    }
    
    const shareText = favorites.map(item => `${item.name} - ${item.description}`).join('\n')
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        util.showSuccess('收藏列表已复制到剪贴板')
      }
    })
  },

  // 导出收藏列表（模拟功能）
  onExportFavorites() {
    const { favorites } = this.data
    
    if (favorites.length === 0) {
      util.showError('收藏列表为空')
      return
    }
    
    util.showLoading('导出中...')
    
    setTimeout(() => {
      util.hideLoading()
      util.showSuccess('导出成功（模拟功能）')
    }, 1500)
  },

  // 景点卡片点击
  onAttractionTap(e) {
    if (this.data.editMode) {
      this.onItemSelect(e)
      return
    }
    
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 取消收藏
  onRemoveFavorite(e) {
    // 安全检查事件对象
    if (e && e.stopPropagation) {
      e.stopPropagation()
    }

    // 检查事件对象和数据
    if (!e || !e.currentTarget || !e.currentTarget.dataset) {
      util.showError('操作失败，请重试')
      return
    }

    const { id, name } = e.currentTarget.dataset
    if (!id || !name) {
      util.showError('景点信息不完整')
      return
    }

    util.showConfirm(`确定要取消收藏"${name}"吗？`).then(confirmed => {
      if (confirmed) {
        if (app.removeFavorite(id)) {
          util.showSuccess('已取消收藏')
          this.loadFavorites()
        }
      }
    })
  },

  // 导航到景点
  onNavigateTap(e) {
    // 安全检查事件对象
    if (e && e.stopPropagation) {
      e.stopPropagation()
    }

    // 检查事件对象和数据
    if (!e || !e.currentTarget || !e.currentTarget.dataset) {
      util.showError('操作失败，请重试')
      return
    }

    const { attraction } = e.currentTarget.dataset
    if (!attraction || !attraction.location) {
      util.showError('暂无位置信息')
      return
    }
    
    const { latitude, longitude } = attraction.location
    
    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: attraction.name,
      address: attraction.detailInfo ? attraction.detailInfo.address : '',
      scale: 18,
      success: () => {
        console.log('打开地图成功')
      },
      fail: (err) => {
        console.log('打开地图失败', err)
        util.showError('打开地图失败')
      }
    })
  },

  // 去首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 分享功能
  onShareAppMessage() {
    const { favorites } = this.data
    return {
      title: `我收藏了${favorites.length}个精彩景点`,
      path: '/pages/favorites/favorites'
    }
  }
})
