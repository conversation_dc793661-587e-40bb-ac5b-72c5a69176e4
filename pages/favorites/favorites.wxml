<!--pages/favorites/favorites.wxml-->
<view class="container">
  <!-- 操作栏 -->
  <view class="action-bar">
    <view class="action-left">
      <view class="favorites-count">共{{favorites.length}}个收藏</view>
    </view>
    <view class="action-right">
      <view wx:if="{{!editMode}}" class="action-btn" bindtap="toggleEditMode">
        <view class="btn-icon">✏️</view>
        <view class="btn-text">编辑</view>
      </view>
      <view wx:else class="action-btn" bindtap="toggleEditMode">
        <view class="btn-icon">✅</view>
        <view class="btn-text">完成</view>
      </view>
    </view>
  </view>

  <!-- 编辑模式操作栏 -->
  <view wx:if="{{editMode}}" class="edit-actions">
    <view class="edit-btn" bindtap="onSelectAll">
      <view class="btn-icon">{{selectedItems.length === favorites.length ? '☑️' : '☐'}}</view>
      <view class="btn-text">全选</view>
    </view>
    <view class="edit-btn" bindtap="onDeleteSelected">
      <view class="btn-icon">🗑️</view>
      <view class="btn-text">删除({{selectedItems.length}})</view>
    </view>
  </view>

  <!-- 功能按钮 -->
  <view wx:if="{{!editMode && favorites.length > 0}}" class="function-buttons">
    <view class="function-btn" bindtap="onShareFavorites">
      <view class="btn-icon">📤</view>
      <view class="btn-text">分享列表</view>
    </view>
    <view class="function-btn" bindtap="onExportFavorites">
      <view class="btn-icon">📋</view>
      <view class="btn-text">导出列表</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 收藏列表 -->
  <view wx:else class="favorites-list">
    <view 
      wx:for="{{favorites}}" 
      wx:key="id" 
      class="favorite-card {{editMode ? 'edit-mode' : ''}} {{selectedItems.includes(item.id) ? 'selected' : ''}}"
      data-id="{{item.id}}"
      bindtap="onAttractionTap"
    >
      <!-- 选择框 -->
      <view wx:if="{{editMode}}" class="select-checkbox">
        <view class="checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}">
          {{selectedItems.includes(item.id) ? '✓' : ''}}
        </view>
      </view>

      <!-- 景点图片 -->
      <view class="card-image-wrapper">
        <image class="card-image" src="{{item.image}}" mode="aspectFill" lazy-load />
        <view wx:if="{{!editMode}}" class="remove-btn" data-id="{{item.id}}" data-name="{{item.name}}" bindtap="onRemoveFavorite">
          <view class="remove-icon">❌</view>
        </view>
      </view>

      <!-- 景点信息 -->
      <view class="card-content">
        <view class="card-header">
          <view class="attraction-name">{{item.name}}</view>
          <view class="attraction-rating">
            <view class="rating-star">⭐</view>
            <view class="rating-value">{{item.rating}}</view>
          </view>
        </view>

        <view class="attraction-info">
          <view class="info-item">
            <view class="info-icon">📍</view>
            <view class="info-text">{{item.distance}}</view>
          </view>
          <view class="info-item">
            <view class="info-icon">💰</view>
            <view class="info-text">{{item.price}}</view>
          </view>
          <view class="info-item">
            <view class="info-icon">🕐</view>
            <view class="info-text">{{item.openTime}}</view>
          </view>
        </view>

        <view class="attraction-desc">{{item.description}}</view>

        <view class="card-actions">
          <view class="attraction-tags">
            <view 
              wx:for="{{item.tags}}" 
              wx:for-item="tag" 
              wx:key="*this" 
              class="tag"
            >
              {{tag}}
            </view>
          </view>
          <view wx:if="{{!editMode}}" class="navigate-btn" data-attraction="{{item}}" bindtap="onNavigateTap">
            <view class="navigate-icon">🧭</view>
            <view class="navigate-text">导航</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{favorites.length === 0 && !loading}}" class="empty-state">
      <view class="empty-state-icon">💝</view>
      <view class="empty-state-text">还没有收藏任何景点</view>
      <view class="empty-state-desc">去首页发现精彩景点吧</view>
      <view class="goto-home-btn" bindtap="goToHome">
        <view class="btn-text">去首页看看</view>
      </view>
    </view>
  </view>
</view>
