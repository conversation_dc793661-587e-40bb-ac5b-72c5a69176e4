// pages/index/index.js
const app = getApp()
const api = require('../../utils/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    searchValue: '',
    attractions: [],
    loading: true,
    refreshing: false
  },

  onLoad() {
    this.loadRecommendedAttractions()
  },

  onShow() {
    // 页面显示时刷新收藏状态
    this.updateFavoriteStatus()
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadRecommendedAttractions().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  // 加载推荐景点
  loadRecommendedAttractions() {
    this.setData({ loading: true })
    
    return api.getAttractions({ recommended: true }).then(res => {
      if (res.success) {
        const attractions = res.data.map(item => ({
          ...item,
          isFavorite: app.isFavorite(item.id)
        }))
        this.setData({ 
          attractions,
          loading: false 
        })
      } else {
        util.showError('加载失败')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('加载推荐景点失败', err)
      util.showError('网络错误')
      this.setData({ loading: false })
    })
  },

  // 更新收藏状态
  updateFavoriteStatus() {
    const attractions = this.data.attractions.map(item => ({
      ...item,
      isFavorite: app.isFavorite(item.id)
    }))
    this.setData({ attractions })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ searchValue: e.detail.value })
  },

  // 搜索确认
  onSearchConfirm() {
    const keyword = this.data.searchValue.trim()
    if (!keyword) {
      util.showError('请输入搜索关键词')
      return
    }
    
    this.performSearch(keyword)
  },

  // 执行搜索
  performSearch(keyword) {
    util.showLoading('搜索中...')
    
    api.getAttractions({ keyword }).then(res => {
      util.hideLoading()
      if (res.success) {
        if (res.data.length === 0) {
          util.showError('未找到相关景点')
          return
        }
        
        const attractions = res.data.map(item => ({
          ...item,
          isFavorite: app.isFavorite(item.id)
        }))
        this.setData({ attractions })
      } else {
        util.showError('搜索失败')
      }
    }).catch(err => {
      util.hideLoading()
      console.error('搜索失败', err)
      util.showError('网络错误')
    })
  },

  // 清空搜索
  onSearchClear() {
    this.setData({ searchValue: '' })
    this.loadRecommendedAttractions()
  },

  // 跳转到附近景点
  goToNearby() {
    wx.switchTab({
      url: '/pages/nearby/nearby'
    })
  },

  // 跳转到收藏页面
  goToFavorites() {
    wx.switchTab({
      url: '/pages/favorites/favorites'
    })
  },

  // 景点卡片点击
  onAttractionTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 收藏按钮点击
  onFavoriteTap(e) {
    // 安全检查事件对象
    if (e && e.stopPropagation) {
      e.stopPropagation()
    }

    // 检查事件对象和数据
    if (!e || !e.currentTarget || !e.currentTarget.dataset) {
      util.showError('操作失败，请重试')
      return
    }

    const { attraction } = e.currentTarget.dataset
    if (!attraction) {
      util.showError('景点信息不存在')
      return
    }

    const { id, isFavorite } = attraction

    if (isFavorite) {
      // 取消收藏
      if (app.removeFavorite(id)) {
        util.showSuccess('已取消收藏')
        this.updateFavoriteStatus()
      }
    } else {
      // 添加收藏
      if (app.addFavorite(attraction)) {
        util.showSuccess('已添加收藏')
        this.updateFavoriteStatus()
      } else {
        util.showError('已经收藏过了')
      }
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '景点助手 - 发现身边的美景',
      path: '/pages/index/index',
      imageUrl: '/images/share-bg.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '景点助手 - 发现身边的美景',
      imageUrl: '/images/share-bg.jpg'
    }
  }
})
