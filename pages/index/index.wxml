<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-input-wrapper">
        <input 
          class="search-input" 
          placeholder="搜索景点名称..." 
          value="{{searchValue}}"
          bindinput="onSearchInput"
          bindconfirm="onSearchConfirm"
          confirm-type="search"
        />
        <view class="search-icon" bindtap="onSearchConfirm">🔍</view>
        <view wx:if="{{searchValue}}" class="clear-icon" bindtap="onSearchClear">✕</view>
      </view>
    </view>
  </view>

  <!-- 快速导航 -->
  <view class="quick-nav">
    <view class="nav-item" bindtap="goToNearby">
      <view class="nav-icon">📍</view>
      <view class="nav-text">附近景点</view>
    </view>
    <view class="nav-item" bindtap="goToFavorites">
      <view class="nav-icon">❤️</view>
      <view class="nav-text">我的收藏</view>
    </view>
  </view>

  <!-- 推荐景点标题 -->
  <view class="section-title">
    <view class="title-text">🌟 推荐景点</view>
    <view class="title-desc">精选热门景点，不容错过</view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 景点列表 -->
  <view wx:else class="attractions-list">
    <view 
      wx:for="{{attractions}}" 
      wx:key="id" 
      class="attraction-card"
      data-id="{{item.id}}"
      bindtap="onAttractionTap"
    >
      <!-- 景点图片 -->
      <view class="card-image-wrapper">
        <image class="card-image" src="{{item.image}}" mode="aspectFill" lazy-load />
        <view class="favorite-btn" data-attraction="{{item}}" bindtap="onFavoriteTap">
          <view class="favorite-icon {{item.isFavorite ? 'active' : ''}}">
            {{item.isFavorite ? '❤️' : '🤍'}}
          </view>
        </view>
        <view wx:if="{{item.isRecommended}}" class="recommend-badge">推荐</view>
      </view>

      <!-- 景点信息 -->
      <view class="card-content">
        <view class="card-header">
          <view class="attraction-name">{{item.name}}</view>
          <view class="attraction-rating">
            <view class="rating-star">⭐</view>
            <view class="rating-value">{{item.rating}}</view>
          </view>
        </view>

        <view class="attraction-info">
          <view class="info-item">
            <view class="info-icon">📍</view>
            <view class="info-text">{{item.distance}}</view>
          </view>
          <view class="info-item">
            <view class="info-icon">💰</view>
            <view class="info-text">{{item.price}}</view>
          </view>
          <view class="info-item">
            <view class="info-icon">🕐</view>
            <view class="info-text">{{item.openTime}}</view>
          </view>
        </view>

        <view class="attraction-desc">{{item.description}}</view>

        <view class="attraction-tags">
          <view 
            wx:for="{{item.tags}}" 
            wx:for-item="tag" 
            wx:key="*this" 
            class="tag"
          >
            {{tag}}
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{attractions.length === 0 && !loading}}" class="empty-state">
      <view class="empty-state-icon">🏞️</view>
      <view class="empty-state-text">暂无推荐景点</view>
    </view>
  </view>
</view>
