/* pages/index/index.wxss */
.container {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 搜索栏样式 */
.search-section {
  padding: 30rpx 20rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.search-bar {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 0 100rpx 0 50rpx;
  backdrop-filter: blur(10rpx);
}

.search-input {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.search-icon {
  position: absolute;
  right: 50rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #666;
}

.clear-icon {
  position: absolute;
  right: 100rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #999;
  padding: 10rpx;
}

/* 快速导航样式 */
.quick-nav {
  display: flex;
  padding: 0 20rpx 30rpx;
  gap: 20rpx;
}

.nav-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.nav-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.nav-text {
  font-size: 26rpx;
  color: #fff;
  font-weight: 500;
}

/* 标题样式 */
.section-title {
  padding: 30rpx 20rpx 20rpx;
  background: #f8f9fa;
  margin-top: -20rpx;
  border-radius: 20rpx 20rpx 0 0;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.title-desc {
  font-size: 24rpx;
  color: #666;
}

/* 景点列表样式 */
.attractions-list {
  padding: 0 20rpx 40rpx;
  background: #f8f9fa;
}

.attraction-card {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.attraction-card:active {
  transform: scale(0.98);
}

/* 图片区域 */
.card-image-wrapper {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.favorite-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.favorite-icon {
  font-size: 32rpx;
  transition: transform 0.2s ease;
}

.favorite-icon.active {
  transform: scale(1.2);
}

.recommend-badge {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

/* 内容区域 */
.card-content {
  padding: 30rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.attraction-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.attraction-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-star {
  font-size: 24rpx;
}

.rating-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff9500;
}

/* 信息行 */
.attraction-info {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-icon {
  font-size: 24rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
}

/* 描述文本 */
.attraction-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 标签 */
.attraction-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.tag {
  background: #f0f8ff;
  color: #4A90E2;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: 1rpx solid #e6f3ff;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: #f8f9fa;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}
