/**app.wxss**/
/* 全局样式 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  color: #333;
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

/* 按钮样式 */
.btn {
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  outline: none;
}

.btn-primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #e9ecef;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid #4A90E2;
  color: #4A90E2;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-medium {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-large {
  padding: 20rpx 40rpx;
  font-size: 32rpx;
}

/* 文本样式 */
.text-primary {
  color: #4A90E2;
}

.text-secondary {
  color: #6c757d;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-muted {
  color: #999;
}

.text-small {
  font-size: 24rpx;
}

.text-medium {
  font-size: 28rpx;
}

.text-large {
  font-size: 32rpx;
}

.text-bold {
  font-weight: 600;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  justify-content: space-around;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-state-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-state-text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

/* 阴影效果 */
.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.shadow-lg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

/* 圆角样式 */
.rounded {
  border-radius: 8rpx;
}

.rounded-lg {
  border-radius: 16rpx;
}

.rounded-full {
  border-radius: 50%;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
