// app.js
App({
  globalData: {
    userInfo: null,
    favorites: [], // 收藏的景点列表
    userLocation: null, // 用户位置信息
    systemInfo: null // 系统信息
  },

  onLaunch() {
    // 获取系统信息
    this.getSystemInfo();
    
    // 从本地存储加载收藏数据
    this.loadFavorites();
    
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || [];
    logs.unshift(Date.now());
    wx.setStorageSync('logs', logs);

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        console.log('登录成功', res.code);
      }
    });
  },

  onShow() {
    // 小程序显示时触发
    console.log('小程序显示');
  },

  onHide() {
    // 小程序隐藏时触发
    console.log('小程序隐藏');
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息', res);
      }
    });
  },

  // 从本地存储加载收藏数据
  loadFavorites() {
    try {
      const favorites = wx.getStorageSync('favorites');
      if (favorites) {
        this.globalData.favorites = favorites;
      }
    } catch (e) {
      console.error('加载收藏数据失败', e);
    }
  },

  // 保存收藏数据到本地存储
  saveFavorites() {
    try {
      wx.setStorageSync('favorites', this.globalData.favorites);
    } catch (e) {
      console.error('保存收藏数据失败', e);
    }
  },

  // 添加收藏
  addFavorite(attraction) {
    const favorites = this.globalData.favorites;
    const index = favorites.findIndex(item => item.id === attraction.id);
    
    if (index === -1) {
      favorites.push(attraction);
      this.saveFavorites();
      return true;
    }
    return false;
  },

  // 移除收藏
  removeFavorite(attractionId) {
    const favorites = this.globalData.favorites;
    const index = favorites.findIndex(item => item.id === attractionId);
    
    if (index !== -1) {
      favorites.splice(index, 1);
      this.saveFavorites();
      return true;
    }
    return false;
  },

  // 检查是否已收藏
  isFavorite(attractionId) {
    return this.globalData.favorites.some(item => item.id === attractionId);
  },

  // 获取用户位置
  getUserLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.globalData.userLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          resolve(res);
        },
        fail: (err) => {
          console.error('获取位置失败', err);
          reject(err);
        }
      });
    });
  },

  // 计算两点间距离（简化版）
  calculateDistance(lat1, lng1, lat2, lng2) {
    const radLat1 = lat1 * Math.PI / 180.0;
    const radLat2 = lat2 * Math.PI / 180.0;
    const a = radLat1 - radLat2;
    const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
      Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    return s;
  },

  // 格式化距离显示
  formatDistance(distance) {
    if (distance < 1) {
      return Math.round(distance * 1000) + 'm';
    } else {
      return distance.toFixed(1) + 'km';
    }
  }
});
