# 图片资源说明

这个目录包含小程序所需的图片资源。

## 需要的图标文件

由于这是一个演示项目，以下图标文件需要您自行添加：

### TabBar 图标 (建议尺寸: 81x81px)
- `home.png` - 首页图标（未选中状态）
- `home-active.png` - 首页图标（选中状态）
- `location.png` - 位置图标（未选中状态）
- `location-active.png` - 位置图标（选中状态）
- `heart.png` - 收藏图标（未选中状态）
- `heart-active.png` - 收藏图标（选中状态）

### 其他图片
- `share-bg.jpg` - 分享背景图

## 图标获取建议

1. **免费图标资源**：
   - Iconfont (阿里巴巴矢量图标库)
   - Feather Icons
   - Heroicons
   - Tabler Icons

2. **设计工具**：
   - Figma
   - Sketch
   - Adobe Illustrator

3. **在线工具**：
   - Canva
   - 稿定设计

## 注意事项

- TabBar 图标建议使用 PNG 格式
- 图标尺寸建议为 81x81px（微信小程序推荐尺寸）
- 确保图标在浅色和深色背景下都有良好的可见性
- 选中和未选中状态的图标应该有明显的视觉区别

## 临时解决方案

如果暂时没有图标文件，可以：
1. 使用纯色的正方形图片作为占位符
2. 或者修改 `app.json` 中的 tabBar 配置，移除 iconPath 和 selectedIconPath 字段
