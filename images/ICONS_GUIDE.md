# 📱 TabBar 图标设计指南

## 🎨 图标概览

为景点助手微信小程序设计了三套 TabBar 图标，每套包含普通状态和激活状态两个版本。

### 图标列表

| 功能 | 普通状态 | 激活状态 | 设计说明 |
|------|----------|----------|----------|
| 首页 | `home.png` | `home-active.png` | 房屋图标，代表首页入口 |
| 附近 | `location.png` | `location-active.png` | 地图标记，代表位置和附近功能 |
| 收藏 | `heart.png` | `heart-active.png` | 心形图标，代表收藏和喜爱 |

## 🎯 设计规范

### 尺寸规格
- **画布尺寸**: 81x81 像素
- **图标尺寸**: 适中，留有合适的边距
- **格式**: PNG（微信小程序要求）
- **源文件**: SVG（便于后续修改）

### 颜色方案
- **普通状态**: `#7A7E83` (灰色)
- **激活状态**: `#4A90E2` (蓝色)
- **背景**: 透明

### 设计风格
- **风格**: 线性图标 + 填充
- **线条**: 圆润，stroke-width: 2-3px
- **形状**: 简洁明了，易于识别
- **视觉**: 统一的设计语言

## 📁 文件结构

```
images/
├── home.svg              # 首页图标源文件
├── home.png              # 首页图标 PNG
├── home-active.svg       # 首页激活图标源文件
├── home-active.png       # 首页激活图标 PNG
├── location.svg          # 位置图标源文件
├── location.png          # 位置图标 PNG
├── location-active.svg   # 位置激活图标源文件
├── location-active.png   # 位置激活图标 PNG
├── heart.svg             # 收藏图标源文件
├── heart.png             # 收藏图标 PNG
├── heart-active.svg      # 收藏激活图标源文件
└── heart-active.png      # 收藏激活图标 PNG
```

## 🔧 生成过程

### 1. SVG 设计
使用 SVG 格式设计图标，便于缩放和修改：

```svg
<svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 图标路径 -->
  <path d="..." fill="#7A7E83" stroke="#7A7E83" stroke-width="2"/>
</svg>
```

### 2. PNG 转换
使用 ImageMagick 将 SVG 转换为 PNG：

```bash
magick home.svg -resize 81x81 home.png
magick home-active.svg -resize 81x81 home-active.png
# ... 其他图标
```

### 3. 配置应用
在 `app.json` 中配置 TabBar 图标：

```json
{
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "images/home.png",
        "selectedIconPath": "images/home-active.png",
        "text": "首页"
      }
    ]
  }
}
```

## 🎨 图标详细设计

### 1. 首页图标 (Home)
**设计元素**: 房屋轮廓
- 三角形屋顶
- 矩形房屋主体
- 门和窗户细节
- 简洁的线条设计

**寓意**: 代表首页、主页、起始点

### 2. 位置图标 (Location)
**设计元素**: 地图标记
- 水滴形状的外轮廓
- 内部圆形标记
- 底部尖角指向位置
- 经典的地图标记样式

**寓意**: 代表位置、导航、附近功能

### 3. 收藏图标 (Heart)
**设计元素**: 心形
- 经典的心形轮廓
- 流畅的曲线设计
- 对称的形状
- 普遍认知的收藏符号

**寓意**: 代表收藏、喜爱、保存

## 🔄 修改指南

### 如需修改图标：

1. **编辑 SVG 文件**
   - 修改对应的 `.svg` 文件
   - 保持 81x81 的 viewBox
   - 使用统一的颜色方案

2. **重新生成 PNG**
   ```bash
   cd images/
   magick 图标名.svg -resize 81x81 图标名.png
   ```

3. **测试效果**
   - 在微信开发者工具中查看效果
   - 确保在不同设备上显示正常

### 颜色修改：
- 普通状态：修改 `fill` 和 `stroke` 为 `#7A7E83`
- 激活状态：修改 `fill` 和 `stroke` 为 `#4A90E2`

## ✅ 质量检查

### 图标要求：
- [x] 尺寸：81x81 像素
- [x] 格式：PNG
- [x] 背景：透明
- [x] 颜色：符合设计规范
- [x] 清晰度：在小尺寸下清晰可见
- [x] 一致性：风格统一

### 微信小程序要求：
- [x] 图标路径正确
- [x] 文件大小合理
- [x] 支持 TabBar 显示
- [x] 普通和激活状态区分明显

## 🎉 使用效果

配置完成后，TabBar 将显示：
- **首页**: 房屋图标，点击后变为蓝色
- **附近**: 位置标记图标，点击后变为蓝色
- **收藏**: 心形图标，点击后变为蓝色

图标设计简洁明了，符合微信小程序的设计规范，提升了用户体验和应用的专业度。
