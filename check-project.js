// check-project.js - 项目结构检查脚本
// 这个脚本可以在 Node.js 环境中运行，用于检查项目文件是否完整

const fs = require('fs');
const path = require('path');

// 检查文件是否存在
function checkFile(filePath) {
  try {
    fs.accessSync(filePath, fs.constants.F_OK);
    return true;
  } catch (err) {
    return false;
  }
}

// 必需的文件列表
const requiredFiles = [
  // 核心文件
  'app.js',
  'app.json',
  'app.wxss',
  'project.config.json',
  'sitemap.json',
  
  // 工具文件
  'utils/util.js',
  'utils/api.js',
  
  // 首页
  'pages/index/index.js',
  'pages/index/index.wxml',
  'pages/index/index.wxss',
  'pages/index/index.json',
  
  // 附近景点页面
  'pages/nearby/nearby.js',
  'pages/nearby/nearby.wxml',
  'pages/nearby/nearby.wxss',
  'pages/nearby/nearby.json',
  
  // 收藏页面
  'pages/favorites/favorites.js',
  'pages/favorites/favorites.wxml',
  'pages/favorites/favorites.wxss',
  'pages/favorites/favorites.json',
  
  // 景点详情页面
  'pages/detail/detail.js',
  'pages/detail/detail.wxml',
  'pages/detail/detail.wxss',
  'pages/detail/detail.json',
  
  // 文档文件
  'README.md',
  'DEVELOPMENT.md',
  'PROJECT_SUMMARY.md'
];

console.log('🔍 检查景点助手微信小程序项目结构...\n');

let allFilesExist = true;
let missingFiles = [];

requiredFiles.forEach(file => {
  if (checkFile(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件缺失`);
    allFilesExist = false;
    missingFiles.push(file);
  }
});

console.log('\n📊 检查结果:');
console.log(`总文件数: ${requiredFiles.length}`);
console.log(`存在文件: ${requiredFiles.length - missingFiles.length}`);
console.log(`缺失文件: ${missingFiles.length}`);

if (allFilesExist) {
  console.log('\n🎉 项目结构完整！所有必需文件都存在。');
  console.log('\n📱 可以在微信开发者工具中打开项目进行测试。');
} else {
  console.log('\n⚠️  项目结构不完整，缺失以下文件:');
  missingFiles.forEach(file => {
    console.log(`   - ${file}`);
  });
}

console.log('\n🚀 使用说明:');
console.log('1. 使用微信开发者工具打开项目根目录');
console.log('2. 配置小程序 AppID（可使用测试号）');
console.log('3. 点击编译运行');
console.log('4. 在模拟器或真机上测试功能');

// 检查 app.json 配置
try {
  const appJsonContent = fs.readFileSync('app.json', 'utf8');
  const appConfig = JSON.parse(appJsonContent);
  
  console.log('\n📋 App.json 配置检查:');
  console.log(`✅ 页面数量: ${appConfig.pages.length}`);
  console.log(`✅ TabBar 配置: ${appConfig.tabBar ? '已配置' : '未配置'}`);
  console.log(`✅ 权限配置: ${appConfig.permission ? '已配置' : '未配置'}`);
  
} catch (err) {
  console.log('\n❌ 无法读取 app.json 配置文件');
}

console.log('\n💡 提示: 如果遇到图标文件缺失的问题，请参考 images/README.md 文件说明。');
