// utils/api.js
const util = require('./util.js')

// 模拟景点数据
const mockAttractions = [
  {
    id: 1,
    name: "故宫博物院",
    image: "https://images.unsplash.com/photo-1508804185872-d7badad00f7d?w=400",
    rating: 4.8,
    distance: "2.3km",
    price: "60元",
    openTime: "08:30-17:00",
    description: "明清两朝的皇家宫殿，现为故宫博物院。是中国古代宫廷建筑之精华，世界现存最大、最完整的木质结构古建筑群。",
    location: {
      latitude: 39.9163,
      longitude: 116.3972
    },
    tags: ["历史文化", "古建筑", "博物馆"],
    isRecommended: true,
    detailInfo: {
      address: "北京市东城区景山前街4号",
      phone: "010-85007421",
      website: "https://www.dpm.org.cn",
      facilities: ["停车场", "餐厅", "纪念品店", "无障碍通道"],
      tips: ["建议游览时间3-4小时", "需要提前预约", "周一闭馆"],
      history: "故宫始建于明永乐四年（1406年），永乐十八年（1420年）建成，曾居住过24位皇帝。"
    }
  },
  {
    id: 2,
    name: "天坛公园",
    image: "https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=400",
    rating: 4.6,
    distance: "3.8km",
    price: "15元",
    openTime: "06:00-22:00",
    description: "明清两朝皇帝祭天的场所，以其精美的建筑和深厚的文化内涵著称。",
    location: {
      latitude: 39.8828,
      longitude: 116.4067
    },
    tags: ["历史文化", "公园", "古建筑"],
    isRecommended: true,
    detailInfo: {
      address: "北京市东城区天坛路甲1号",
      phone: "010-67028866",
      website: "http://www.tiantanpark.com",
      facilities: ["停车场", "公厕", "小卖部"],
      tips: ["早晨有很多晨练的市民", "祈年殿是标志性建筑", "可以体验回音壁"],
      history: "天坛始建于明永乐十八年（1420年），是明清两朝皇帝祭天祈谷的重要场所。"
    }
  },
  {
    id: 3,
    name: "颐和园",
    image: "https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=400",
    rating: 4.7,
    distance: "15.2km",
    price: "30元",
    openTime: "06:30-18:00",
    description: "中国现存规模最大、保存最完整的皇家园林，被誉为皇家园林博物馆。",
    location: {
      latitude: 39.9998,
      longitude: 116.2754
    },
    tags: ["皇家园林", "湖泊", "古建筑"],
    isRecommended: true,
    detailInfo: {
      address: "北京市海淀区新建宫门路19号",
      phone: "010-62881144",
      website: "http://www.summerpalace-china.com",
      facilities: ["停车场", "游船", "餐厅", "纪念品店"],
      tips: ["可以乘船游昆明湖", "长廊是必看景点", "春夏季节最美"],
      history: "颐和园前身为清漪园，始建于1750年，1860年被英法联军焚毁，1886年重建并改名颐和园。"
    }
  },
  {
    id: 4,
    name: "八达岭长城",
    image: "https://images.unsplash.com/photo-1508804185872-d7badad00f7d?w=400",
    rating: 4.5,
    distance: "65.8km",
    price: "40元",
    openTime: "07:00-18:00",
    description: "万里长城的重要组成部分，是明长城的一个隘口，被称为天下九塞之一。",
    location: {
      latitude: 40.3584,
      longitude: 116.0134
    },
    tags: ["长城", "历史文化", "登山"],
    isRecommended: false,
    detailInfo: {
      address: "北京市延庆区G6京藏高速58号出口",
      phone: "010-69121383",
      website: "http://www.badaling.cn",
      facilities: ["缆车", "停车场", "餐厅", "纪念品店"],
      tips: ["建议穿舒适的运动鞋", "可选择缆车上山", "避开节假日人流高峰"],
      history: "八达岭长城建于明弘治十八年（1505年），是明长城向游人开放最早的地段。"
    }
  },
  {
    id: 5,
    name: "南锣鼓巷",
    image: "https://images.unsplash.com/photo-1545558014-8692077e9b5c?w=400",
    rating: 4.2,
    distance: "4.5km",
    price: "免费",
    openTime: "全天开放",
    description: "北京最古老的街区之一，保存着元大都时期的胡同格局，充满老北京风情。",
    location: {
      latitude: 39.9368,
      longitude: 116.4034
    },
    tags: ["胡同", "文化街区", "美食"],
    isRecommended: false,
    detailInfo: {
      address: "北京市东城区南锣鼓巷",
      phone: "010-64040617",
      website: "",
      facilities: ["小吃店", "咖啡厅", "文创店", "酒吧"],
      tips: ["适合下午和晚上游览", "有很多特色小店", "可以品尝老北京小吃"],
      history: "南锣鼓巷建于元朝，距今已有740多年历史，是北京最古老的街区之一。"
    }
  },
  {
    id: 6,
    name: "什刹海",
    image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400",
    rating: 4.4,
    distance: "6.2km",
    price: "免费",
    openTime: "全天开放",
    description: "由前海、后海、西海组成的什刹海，是北京内城唯一一处具有开阔水面的开放型景区。",
    location: {
      latitude: 39.9368,
      longitude: 116.3831
    },
    tags: ["湖泊", "酒吧街", "胡同"],
    isRecommended: false,
    detailInfo: {
      address: "北京市西城区什刹海",
      phone: "010-83288149",
      website: "",
      facilities: ["游船", "酒吧", "餐厅", "自行车租赁"],
      tips: ["夜景很美", "可以坐三轮车游胡同", "有很多酒吧和餐厅"],
      history: "什刹海在元朝时是大都城内的重要水域，明清时期成为皇家园林的一部分。"
    }
  }
]

// AI助手的预设回答
const aiResponses = {
  greeting: [
    "您好！我是您的景点助手，很高兴为您服务！有什么关于景点的问题我可以帮您解答吗？",
    "欢迎使用景点助手！我可以为您介绍景点信息、推荐游览路线、解答各种问题。请问您想了解什么？",
    "您好！我是专业的景点导览助手，可以为您提供详细的景点介绍和游览建议。有什么可以帮您的吗？"
  ],
  openTime: [
    "这个景点的开放时间是{openTime}，建议您提前规划好行程哦！",
    "开放时间：{openTime}。请注意，节假日可能会有调整，建议出行前确认一下。",
    "营业时间是{openTime}，为了避免人流高峰，建议您选择合适的时间前往。"
  ],
  price: [
    "门票价格是{price}，性价比还是很不错的！",
    "票价：{price}。建议您关注官方渠道，有时会有优惠活动。",
    "这里的门票是{price}，相比其他同类景点来说价格比较合理。"
  ],
  traffic: [
    "您可以乘坐地铁、公交或打车前往。建议使用导航软件规划最佳路线。",
    "交通还是比较便利的，可以选择地铁、公交、出租车等多种方式。推荐使用公共交通，既环保又避免停车难题。",
    "建议您使用高德地图或百度地图导航，会为您推荐最优路线和交通方式。"
  ],
  history: [
    "这个景点有着悠久的历史文化底蕴。{history}",
    "从历史角度来看，{history}这里承载着丰富的文化内涵。",
    "让我为您介绍一下它的历史背景：{history}"
  ],
  recommendation: [
    "根据这个景点的特色，我建议您：1. 提前了解相关历史背景；2. 合理安排游览时间；3. 注意保护文物古迹。",
    "游览建议：建议您选择人流较少的时段前往，这样可以更好地欣赏景点的美景和文化内涵。",
    "推荐游览路线：可以先参观主要景点，再逐步深入了解细节。记得带好相机记录美好时光！"
  ],
  facilities: [
    "这里的配套设施包括：{facilities}，基本能满足游客的需求。",
    "景区设施：{facilities}。整体来说设施比较完善，游览体验不错。",
    "配套服务：{facilities}，为游客提供了便利的服务。"
  ],
  tips: [
    "游览小贴士：{tips}",
    "温馨提示：{tips}",
    "实用建议：{tips}"
  ],
  default: [
    "这是一个很有特色的景点，值得一游！如果您有具体问题，我可以为您详细解答。",
    "每个景点都有其独特的魅力，建议您实地体验一下。有什么特别想了解的吗？",
    "我会尽力为您提供有用的信息。您可以问我关于开放时间、门票价格、交通路线等任何问题。"
  ]
}

// 获取景点列表
const getAttractions = (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let result = [...mockAttractions]
      
      // 筛选推荐景点
      if (params.recommended) {
        result = result.filter(item => item.isRecommended)
      }
      
      // 搜索功能
      if (params.keyword) {
        const keyword = params.keyword.toLowerCase()
        result = result.filter(item => 
          item.name.toLowerCase().includes(keyword) ||
          item.description.toLowerCase().includes(keyword) ||
          item.tags.some(tag => tag.toLowerCase().includes(keyword))
        )
      }
      
      // 排序
      if (params.sortBy) {
        switch (params.sortBy) {
          case 'distance':
            result.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance))
            break
          case 'rating':
            result.sort((a, b) => b.rating - a.rating)
            break
          case 'price':
            result.sort((a, b) => {
              const priceA = a.price === '免费' ? 0 : parseInt(a.price)
              const priceB = b.price === '免费' ? 0 : parseInt(b.price)
              return priceA - priceB
            })
            break
        }
      }
      
      resolve({
        success: true,
        data: result
      })
    }, 500)
  })
}

// 获取景点详情
const getAttractionDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const attraction = mockAttractions.find(item => item.id === id)
      if (attraction) {
        resolve({
          success: true,
          data: attraction
        })
      } else {
        resolve({
          success: false,
          message: '景点不存在'
        })
      }
    }, 300)
  })
}

// AI助手回答
const getAIResponse = (question, attractionInfo = null) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let response = ''
      const lowerQuestion = question.toLowerCase()
      
      // 问候语
      if (lowerQuestion.includes('你好') || lowerQuestion.includes('hello')) {
        response = aiResponses.greeting[util.getRandomInt(0, aiResponses.greeting.length - 1)]
      }
      // 开放时间
      else if (lowerQuestion.includes('时间') || lowerQuestion.includes('开门') || lowerQuestion.includes('营业')) {
        const template = aiResponses.openTime[util.getRandomInt(0, aiResponses.openTime.length - 1)]
        response = attractionInfo ? template.replace('{openTime}', attractionInfo.openTime) : '请先选择一个景点，我就能告诉您具体的开放时间了。'
      }
      // 门票价格
      else if (lowerQuestion.includes('价格') || lowerQuestion.includes('门票') || lowerQuestion.includes('多少钱')) {
        const template = aiResponses.price[util.getRandomInt(0, aiResponses.price.length - 1)]
        response = attractionInfo ? template.replace('{price}', attractionInfo.price) : '请先选择一个景点，我就能告诉您门票价格了。'
      }
      // 交通路线
      else if (lowerQuestion.includes('怎么去') || lowerQuestion.includes('交通') || lowerQuestion.includes('路线')) {
        response = aiResponses.traffic[util.getRandomInt(0, aiResponses.traffic.length - 1)]
      }
      // 历史文化
      else if (lowerQuestion.includes('历史') || lowerQuestion.includes('文化') || lowerQuestion.includes('故事')) {
        const template = aiResponses.history[util.getRandomInt(0, aiResponses.history.length - 1)]
        response = attractionInfo && attractionInfo.detailInfo ? 
          template.replace('{history}', attractionInfo.detailInfo.history) : 
          '这个景点有着深厚的历史文化底蕴，建议您实地参观体验。'
      }
      // 游览建议
      else if (lowerQuestion.includes('建议') || lowerQuestion.includes('推荐') || lowerQuestion.includes('路线')) {
        response = aiResponses.recommendation[util.getRandomInt(0, aiResponses.recommendation.length - 1)]
      }
      // 设施服务
      else if (lowerQuestion.includes('设施') || lowerQuestion.includes('服务') || lowerQuestion.includes('配套')) {
        const template = aiResponses.facilities[util.getRandomInt(0, aiResponses.facilities.length - 1)]
        response = attractionInfo && attractionInfo.detailInfo ? 
          template.replace('{facilities}', attractionInfo.detailInfo.facilities.join('、')) : 
          '景区一般都有基本的配套设施，具体情况建议您查看景点详情。'
      }
      // 游览提示
      else if (lowerQuestion.includes('注意') || lowerQuestion.includes('提示') || lowerQuestion.includes('小贴士')) {
        const template = aiResponses.tips[util.getRandomInt(0, aiResponses.tips.length - 1)]
        response = attractionInfo && attractionInfo.detailInfo ? 
          template.replace('{tips}', attractionInfo.detailInfo.tips.join('；')) : 
          '建议您提前了解景点信息，合理安排游览时间。'
      }
      // 默认回答
      else {
        response = aiResponses.default[util.getRandomInt(0, aiResponses.default.length - 1)]
      }
      
      resolve({
        success: true,
        data: {
          message: response,
          timestamp: new Date().getTime()
        }
      })
    }, 800)
  })
}

module.exports = {
  getAttractions,
  getAttractionDetail,
  getAIResponse
}
